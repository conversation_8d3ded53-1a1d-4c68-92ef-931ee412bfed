<?php
declare(strict_types=1);

namespace CopeX\PostPay\Model\Payment;

use Magento\Payment\Model\Method\AbstractMethod;
use Magento\Quote\Api\Data\CartInterface;

class Postpay extends AbstractMethod
{

    public const CODE = 'postpay';

    protected $_code = self::CODE;
    protected $_isOffline = true;

    protected $_infoBlockType = \CopeX\PostPay\Block\Info\Instructions::class;

    public function isAvailable(CartInterface $quote = null): bool
    {
        return parent::isAvailable($quote) && !$this->quoteHasVirtualProducts($quote);
    }

    public function canUseForCountry($country)
    {
        return $country === 'AT';
    }

    public function canUseForCurrency($currencyCode)
    {
        return $currencyCode === 'EUR';
    }

   public function getConfigData($field, $storeId = null)
   {
       if ($field === 'max_order_total') {
           return 3000;
       }
       return parent::getConfigData($field, $storeId);
   }

    private function quoteHasVirtualProducts(?CartInterface $quote)
    {
        foreach ($quote->getAllItems() ?? [] as $item) {
            if ($item->getProduct()->isVirtual()) {
                return true;
            }
        }
        return false;
    }
}

