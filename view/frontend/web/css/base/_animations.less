@-webkit-keyframes firecheckout-shake {
    10%, 90% {
        -webkit-transform: translate3d(-1px, 0, 0);
    }
    20%, 80% {
        -webkit-transform: translate3d(1px, 0, 0);
    }
    30%, 50%, 70% {
        -webkit-transform: translate3d(-1px, 0, 0);
    }
    40%, 60% {
        -webkit-transform: translate3d(1px, 0, 0);
    }
}
@keyframes firecheckout-shake {
    10%, 90% {
        -webkit-transform: translate3d(-1px, 0, 0);
    }
    20%, 80% {
        -webkit-transform: translate3d(1px, 0, 0);
    }
    30%, 50%, 70% {
        -webkit-transform: translate3d(-1px, 0, 0);
    }
    40%, 60% {
        -webkit-transform: translate3d(1px, 0, 0);
    }
}

.firecheckout-shake {
    animation: firecheckout-shake 0.82s cubic-bezier(.36,.07,.19,.97) both;
    transform: translate3d(0, 0, 0);
    perspective: 1000px;
}

@-webkit-keyframes firecheckout-spin {
    to {
        -webkit-transform: rotate(360deg);
        transform: rotate(360deg);
    }
}

@keyframes firecheckout-spin {
    to {
        -webkit-transform: rotate(360deg);
        transform: rotate(360deg);
    }
}

@keyframes firecheckout-pulse {
    50% {
        opacity: .4;
    }
}
