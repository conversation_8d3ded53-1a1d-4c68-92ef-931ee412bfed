<?php

declare(strict_types=1);

namespace CopeX\PlcLib;

/**
 * Configuration class for PLC API
 */
class PlcConfig
{
    public function __construct(
        private readonly string $clientId,
        private readonly string $orgUnitId,
        private readonly string $orgUnitGuid,
        private readonly bool $testMode = false,
        private readonly string $labelFormat = '100x200',
        private readonly string $language = 'pdf',
        private readonly string $paperLayout = '2xA5inA4',
        private readonly ?string $postPayApiKey = null,
        private readonly ?string $postPayOAuthToken = null
    ) {
        if (empty($this->clientId)) {
            throw new PlcException('Client ID cannot be empty');
        }
        if (empty($this->orgUnitId)) {
            throw new PlcException('Organization Unit ID cannot be empty');
        }
        if (empty($this->orgUnitGuid)) {
            throw new PlcException('Organization Unit GUID cannot be empty');
        }
    }

    public function getClientId(): string
    {
        return $this->clientId;
    }

    public function getOrgUnitId(): string
    {
        return $this->orgUnitId;
    }

    public function getOrgUnitGuid(): string
    {
        return $this->orgUnitGuid;
    }

    public function isTestMode(): bool
    {
        return $this->testMode;
    }

    public function getLabelFormat(): string
    {
        return $this->labelFormat;
    }

    public function getLanguage(): string
    {
        return $this->language;
    }

    public function getPaperLayout(): string
    {
        return $this->paperLayout;
    }

    public function getPostPayApiKey(): ?string
    {
        return $this->postPayApiKey;
    }

    public function getPostPayOAuthToken(): ?string
    {
        return $this->postPayOAuthToken;
    }

    public function getWsdlUrl(): string
    {
        return $this->testMode 
            ? 'https://abn-plc.post.at/DataService/Post.Webservice/ShippingService.svc?WSDL'
            : 'https://plc.post.at/Post.Webservice/ShippingService.svc?WSDL';
    }

    public function getPostPayRefundUrl(): string
    {
        return $this->testMode
            ? 'https://postag.test.apimanagement.ch20.hana.ondemand.com:443/v1/RefundCreation/RESTAdapter/Refund'
            : 'https://postag.prod.apimanagement.ch20.hana.ondemand.com:443/v1/RefundCreation/RESTAdapter/Refund';
    }

    public function getSoapOptions(): array
    {
        return [
            'trace' => true,
            'exceptions' => true,
            'cache_wsdl' => WSDL_CACHE_NONE,
            'features' => SOAP_SINGLE_ELEMENT_ARRAYS | SOAP_USE_XSI_ARRAY_TYPE,
        ];
    }

    public function getPostPayHeaders(): array
    {
        $headers = [
            'Content-Type' => 'application/json',
        ];

        if ($this->postPayApiKey) {
            $headers['APIKey'] = $this->postPayApiKey;
        }

        if ($this->postPayOAuthToken) {
            $headers['Authorization'] = 'Bearer ' . $this->postPayOAuthToken;
        }

        return $headers;
    }
}
