<?php

declare(strict_types=1);

namespace CopeX\PlcLib;

/**
 * PostPay refund data structure
 */
class PlcPostPayRefund
{
    public function __construct(
        private readonly string $trackingNumber,
        private readonly float $refundAmount,
        private readonly string $consigneeFirstName,
        private readonly string $consigneeLastName,
        private readonly string $consigneeStreet,
        private readonly string $consigneePostalCode,
        private readonly string $consigneeCity,
        private readonly string $consigneeEmail,
        private readonly string $reasonForRefund,
        private readonly ?string $validFrom = null,
        private readonly ?float $voucherAmount = null
    ) {
        if (empty($this->trackingNumber)) {
            throw new PlcException('Tracking number cannot be empty');
        }
        if ($this->refundAmount <= 0) {
            throw new PlcException('Refund amount must be positive');
        }
        if (empty($this->consigneeFirstName)) {
            throw new PlcException('Consignee first name cannot be empty');
        }
        if (empty($this->consigneeLastName)) {
            throw new PlcException('Consignee last name cannot be empty');
        }
        if (empty($this->consigneeStreet)) {
            throw new PlcException('Consignee street cannot be empty');
        }
        if (empty($this->consigneePostalCode)) {
            throw new PlcException('Consignee postal code cannot be empty');
        }
        if (empty($this->consigneeCity)) {
            throw new PlcException('Consignee city cannot be empty');
        }
        if (empty($this->consigneeEmail)) {
            throw new PlcException('Consignee email cannot be empty');
        }
        if (!filter_var($this->consigneeEmail, FILTER_VALIDATE_EMAIL)) {
            throw new PlcException('Invalid consignee email format');
        }
        if (empty($this->reasonForRefund)) {
            throw new PlcException('Reason for refund cannot be empty');
        }
        if (strlen($this->trackingNumber) > 22) {
            throw new PlcException('Tracking number cannot exceed 22 characters');
        }
        if (strlen($this->consigneeFirstName) > 40) {
            throw new PlcException('Consignee first name cannot exceed 40 characters');
        }
        if (strlen($this->consigneeLastName) > 40) {
            throw new PlcException('Consignee last name cannot exceed 40 characters');
        }
        if (strlen($this->consigneeStreet) > 60) {
            throw new PlcException('Consignee street cannot exceed 60 characters');
        }
        if (strlen($this->consigneePostalCode) > 10) {
            throw new PlcException('Consignee postal code cannot exceed 10 characters');
        }
        if (strlen($this->consigneeCity) > 35) {
            throw new PlcException('Consignee city cannot exceed 35 characters');
        }
        if (strlen($this->consigneeEmail) > 241) {
            throw new PlcException('Consignee email cannot exceed 241 characters');
        }
        if (strlen($this->reasonForRefund) > 30) {
            throw new PlcException('Reason for refund cannot exceed 30 characters');
        }
    }

    public function getTrackingNumber(): string
    {
        return $this->trackingNumber;
    }

    public function getRefundAmount(): float
    {
        return $this->refundAmount;
    }

    public function getConsigneeFirstName(): string
    {
        return $this->consigneeFirstName;
    }

    public function getConsigneeLastName(): string
    {
        return $this->consigneeLastName;
    }

    public function getConsigneeStreet(): string
    {
        return $this->consigneeStreet;
    }

    public function getConsigneePostalCode(): string
    {
        return $this->consigneePostalCode;
    }

    public function getConsigneeCity(): string
    {
        return $this->consigneeCity;
    }

    public function getConsigneeEmail(): string
    {
        return $this->consigneeEmail;
    }

    public function getReasonForRefund(): string
    {
        return $this->reasonForRefund;
    }

    public function getValidFrom(): ?string
    {
        return $this->validFrom;
    }

    public function getVoucherAmount(): ?float
    {
        return $this->voucherAmount;
    }

    /**
     * Convert to array for API request
     */
    public function toArray(): array
    {
        $data = [
            'trackingNumber' => $this->trackingNumber,
            'refundAmount' => $this->refundAmount,
            'consigneeFirstName' => $this->consigneeFirstName,
            'consigneeLastName' => $this->consigneeLastName,
            'consigneeStreet' => $this->consigneeStreet,
            'consigneePostalCode' => $this->consigneePostalCode,
            'consigneeCity' => $this->consigneeCity,
            'consigneeEmail' => $this->consigneeEmail,
            'reasonForRefund' => $this->reasonForRefund,
        ];

        if ($this->validFrom !== null) {
            $data['validFrom'] = $this->validFrom;
        }

        if ($this->voucherAmount !== null) {
            $data['voucherAmount'] = $this->voucherAmount;
        }

        return $data;
    }
}
